import SwiftUI

/// 卡包视图
///
/// 使用DataManagement作为数据源，通过CardBagVM处理业务逻辑和数据格式化。
struct CardBagView: View {

  // MARK: - 属性

  @ObservedObject private var viewModel: CardBagVM
  @State private var showFilterSheet: Bool = false
  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  // 卡片详情相关状态
  @Namespace private var cardNamespace
  @State private var showingCardDetail = false
  @State private var selectedCard: CardModel? = nil

  // MARK: - 初始化

  init(dataManager: DataManagement) {
    self.viewModel = CardBagVM(dataManager: dataManager)
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
      VStack(spacing: 0) {
        // MARK: 导航栏
        NavigationBarKit(
          viewModel: NavigationBarKitVM(
            title: "卡包",
            backAction: {
              dataManager.hapticManager.trigger(.impactLight)
              dismiss()
            },
            rightButton: .icon(
              "plus",
              action: {
                dataManager.hapticManager.trigger(.impactLight)
                pathManager.path.append(NavigationDestination.cardCategoryView)
              }
            )
          ))

        // MARK: 顶部区域
        topArea

        // MARK: 主要内容区域
        ScrollView(.vertical, showsIndicators: false) {
          VStack(spacing: 0) {
            if !viewModel.filteredCardVMs.isEmpty {
              // MARK: 卡片列表（使用真实数据）
              LazyVStack(spacing: -24) {
                ForEach(viewModel.filteredCardVMs) { cardVM in
                  Card(
                    viewModel: cardVM,
                    onTap: {
                      // 外部点击回调处理
                      dataManager.hapticManager.trigger(.selection)
                      // 从dataManager中找到对应的CardModel
                      if let card = dataManager.cards.first(where: { $0.id.uuidString == cardVM.id }
                      ) {
                        selectedCard = card
                        withAnimation(.easeInOut(duration: 0.3)) {
                          showingCardDetail = true
                        }
                      }
                    }
                  )
                  .shadow(color: .cLightBlue, radius: 0, x: 0, y: -2)
                  // 几何动画效果
                  .matchedGeometryEffect(
                    id: UUID(uuidString: cardVM.id) ?? UUID(),
                    in: cardNamespace,
                    isSource:
                      !(showingCardDetail
                      && selectedCard?.id.uuidString == cardVM.id)
                  )

                  // 过渡动画（保持你选择的动画效果）
                  .transition(
                    .asymmetric(
                      insertion: .move(edge: .trailing).combined(
                        with: .opacity),
                      removal: .move(edge: .leading).combined(with: .opacity)
                    )
                  )

                }
              }
              .padding(.top, 16)
              // 整个卡片列表的过渡动画
              .transition(
                .asymmetric(
                  insertion: .move(edge: .trailing).combined(with: .opacity),
                  removal: .move(edge: .leading).combined(with: .opacity)
                )
              )
            } else {
              // MARK: 404空状态视图
              VStack(spacing: 12) {
                Image("404")
                  .resizable()
                  .frame(width: 80, height: 80)
                Text("暂无\(viewModel.cardTypeText)")
                  .font(.system(size: 14, weight: .regular))
                  .foregroundColor(.cBlack.opacity(0.4))
              }
              .frame(height: 120)
              .frame(maxWidth: .infinity)
              .padding(.top, 60)
              // 与卡片列表相同的过渡动画
              .transition(
                .asymmetric(
                  insertion: .move(edge: .trailing).combined(with: .opacity),
                  removal: .move(edge: .leading).combined(with: .opacity)
                )
              )
            }
          }
        }
        .background(.cLightBlue)
        .clipShape(
          UnevenRoundedRectangle(topLeadingRadius: 36, topTrailingRadius: 36)
        )
        .edgesIgnoringSafeArea(.bottom)
        .shadow(color: .cAccentBlue.opacity(0.05), radius: 10, x: 0, y: -4)
      }
    }
    .background(
      DottedGridBackground()
        .ignoresSafeArea(.all)
    )
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
    .onAppear {
      // 设置卡片点击回调
      setupCardTapHandler()
    }
    .onChange(of: viewModel.filteredCardVMs.count) {
      // 当卡片数量变化时，重新设置回调
      setupCardTapHandler()
    }

    // 筛选弹窗
    .floatingSheet(
      isPresented: $showFilterSheet,
      config: SheetBase(
        maxDetent: .fraction(0.4),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CardFilterActionSheet(
        viewModel: viewModel.createCardFilterActionSheetVM(showFilterSheet: $showFilterSheet)
      )
    }

    // 卡片详情overlay（与正式CardView完全一致）
    .overlay {
      if showingCardDetail, let selectedCard = selectedCard {
        CardDetailView(
          card: selectedCard,
          cardNamespace: cardNamespace,
          showingCardDetail: $showingCardDetail,
          pathManager: pathManager,
          transactions: viewModel.dataManager.allTransactions,
          currencies: viewModel.dataManager.currencies,
          animationMode: .matchedGeometry,
          isCreatingCard: false,
          isCredit: nil,
          mainCategory: nil,
          subCategory: nil,
          onCardCreated: nil,
          dataManager: viewModel.dataManager
        )
      }
    }
  }

  // MARK: - 私有方法

  /// 设置卡片点击回调
  private func setupCardTapHandler() {
    viewModel.setCardTapHandler { card in
      dataManager.hapticManager.trigger(.selection)
      selectedCard = card
      withAnimation(.easeInOut(duration: 0.3)) {
        showingCardDetail = true
      }
    }
  }

  // MARK: - 子视图构建器

  /// 顶部区域
  private var topArea: some View {
    VStack(spacing: 0) {
      // 净资产显示
      VStack(spacing: 0) {
        TitleKit(
          viewModel: TitleKitVM.titleOnly(title: "净资产")
        )
        .opacity(0.6)
        HStack {
          DisplayCurrencyView.size32(
            symbol: viewModel.netAssetSymbol,
            amount: viewModel.netAssetAmount
          )
          Spacer()

          // 筛选按钮
          Button(action: {
            dataManager.hapticManager.trigger(.selection)
            showFilterSheet = true
          }) {
            HStack(spacing: 6) {
              Text(viewModel.filterDisplayText)
                .foregroundColor(.cBlack.opacity(0.6))
              Image("filter")
                .foregroundColor(.cBlack.opacity(0.2))
            }
            .font(.system(size: 12, weight: .regular))
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .cornerRadius(24)
            .overlay(
              RoundedRectangle(cornerRadius: 24)
                .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
            )
          }
        }
        .padding(.horizontal, 16)
      }
      .padding(.bottom, 12)

      // 卡片数量统计
      HStack(spacing: 4) {
        Image("wallet_icon")
          .font(.system(size: 16, weight: .regular))
        Text("你有 \(viewModel.activeCardCount) 张活跃的\(viewModel.cardTypeText)")
          .font(.system(size: 12, weight: .regular))
          .contentTransition(.numericText(value: Double(viewModel.activeCardCount)))
        Spacer()
      }
      .foregroundColor(.cBlack.opacity(0.6))
      .padding(.horizontal, 16)
      .padding(.bottom, 12)
    }
  }

}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct CardBagView_Previews: PreviewProvider {
    static var previews: some View {
      CardBagView(dataManager: createPreviewDataManager())
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      // 创建示例货币
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        )
      ]

      // 创建示例卡片
      let cards = [
        CardModel(
          id: UUID(),
          order: 0,
          isCredit: false,
          isSelected: true,
          name: "招商银行储蓄卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 8580.50,
          credit: 0,
          isStatistics: true,
          cover: "Card_CS_1",
          bankName: "招商银行",
          cardNumber: "1234",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "工商银行信用卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: -2500.00,
          credit: 10000.0,
          isStatistics: true,
          cover: "Card_CS_2",
          bankName: "中国工商银行",
          cardNumber: "5678",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 2,
          isCredit: false,
          isSelected: true,
          name: "支付宝余额",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 1024.88,
          credit: 0,
          isStatistics: true,
          cover: "Card_CS_3",
          bankName: "支付宝",
          cardNumber: "",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]

      return DataManagement(
        cards: cards,
        mainCategories: [],
        subCategories: [],
        currencies: currencies,
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }
  }
#endif
