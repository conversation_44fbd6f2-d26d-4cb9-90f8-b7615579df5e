//
//  ThemeSettingsView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftUI

/// 主题设置页面
///
/// 提供主题模式选择功能的设置页面，采用MVVM架构模式。
/// 支持日间模式、夜间模式和系统自动三种主题选择。
///
/// ## 主要功能
/// - 主题模式选择（日间/夜间/系统自动）
/// - 实时主题预览
/// - 主题切换动画效果
/// - 触觉反馈集成
///
/// ## 设计特点
/// - 清晰的视觉层次和选项展示
/// - 流畅的交互动画
/// - 一致的设计语言
/// - 无障碍访问支持
///
/// ## 使用示例
/// ```swift
/// ThemeSettingsView(viewModel: ThemeSettingsVM(dataManager: dataManager))
/// ```
///
/// - Author: AI Assistant
/// - Since: 2025.8.13
/// - Note: 该视图专注于UI渲染，业务逻辑由ViewModel处理
struct ThemeSettingsView: View {

  // MARK: - Environment Properties

  @Environment(\.dismiss) private var dismiss
  @Environment(\.dataManager) private var dataManager

  // MARK: - State Properties

  /// 当前选中的主题模式，使用@AppStorage自动持久化
  @AppStorage(ThemeHelper.themeKey) private var selectedTheme: String = AppThemeMode.system.rawValue

  // MARK: - Computed Properties

  /// 当前主题模式枚举
  private var currentThemeMode: AppThemeMode {
    AppThemeMode(rawValue: selectedTheme) ?? .system
  }

  // MARK: - Body

  var body: some View {
    VStack(spacing: 0) {
      // MARK: 导航栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "主题设置",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            dismiss()
          }
        )
      )

      // 主要内容区域
      VStack(spacing: 16) {
        // 主题切换行
        HStack {
          Text("主题切换")
            .font(.system(size: 16, weight: .medium))
            .foregroundColor(.cBlack)

          Spacer()

          // 三个主题选项并排
          HStack(spacing: 8) {
            ForEach(AppThemeMode.allCases, id: \.self) { theme in
              ThemeButton(
                theme: theme,
                isSelected: currentThemeMode == theme,
                onTap: {
                  dataManager.hapticManager.trigger(.selection)
                  selectedTheme = theme.rawValue
                }
              )
            }
          }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(.cWhite.opacity(0.5))
        .cornerRadius(12)

        Spacer()
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)
  }

}

// MARK: - Supporting Views

/// 简化的主题按钮
private struct ThemeButton: View {
  let theme: AppThemeMode
  let isSelected: Bool
  let onTap: () -> Void

  var body: some View {
    Button(action: onTap) {
      Image(systemName: theme.iconName)
        .foregroundColor(iconColor)
        .font(.system(size: 18, weight: .medium))
        .frame(width: 40, height: 40)
        .background(backgroundColor)
        .cornerRadius(8)
    }
    .buttonStyle(PlainButtonStyle())
  }

  private var iconColor: Color {
    if isSelected {
      return .white
    }

    switch theme {
    case .light:
      return .orange
    case .dark:
      return .indigo
    case .system:
      return .gray
    }
  }

  private var backgroundColor: Color {
    if isSelected {
      return .cAccentBlue
    } else {
      return .cBlack.opacity(0.05)
    }
  }
}

// MARK: - Preview

#if DEBUG
  struct ThemeSettingsView_Previews: PreviewProvider {
    static var previews: some View {
      ThemeSettingsView()
        .environment(\.dataManager, DataManagement())
    }
  }
#endif
